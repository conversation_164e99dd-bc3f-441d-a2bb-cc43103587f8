package com.example.addon.utils;

import meteordevelopment.meteorclient.MeteorClient;
import meteordevelopment.meteorclient.systems.modules.world.Timer;
import meteordevelopment.meteorclient.utils.player.Rotations;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket.Full;


public class RotationManager implements Base {
   public float lastYaw;
   public float lastPitch;
   public Rotation currentRotation = null;
   public Rotation prevRotation = new Rotation(0.0F, 0.0F);
   public Rotation serverRotation = new Rotation(0.0F, 0.0F);
   Timer rotation_timer = new Timer();

   public RotationManager() {
      MeteorClient.EVENT_BUS.subscribe(this);
   }

   public boolean register(Rotation rotation) {
      if (this.currentRotation != null && this.currentRotation.getPriority() > rotation.getPriority()) {
         return false;
      } else {
         this.currentRotation = rotation;
         this.rotation_timer.reset();
         mc.player
            .networkHandler
            .sendPacket(
               new Full(
                  mc.player.getX(),
                  mc.player.getY(),
                  mc.player.getZ(),
                  rotation.getYaw(),
                  rotation.getPitch(),
                  mc.player.isOnGround()
               )
            );
         Rotations.setCamRotation(rotation.getYaw(), rotation.getPitch());
         return true;
      }
   }

   public void sync() {
      mc.player
         .networkHandler
         .sendPacket(
            new Full(
               mc.player.getX(),
               mc.player.getY(),
               mc.player.getZ(),
               mc.player.getYaw(),
               mc.player.getPitch(),
               mc.player.isOnGround()
            )
         );
      this.currentRotation = null;
   }
}
