package com.example.addon.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import io.github.classgraph.ClassGraph;
import io.github.classgraph.ScanResult;

import java.io.FileWriter;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class ConstantPoolDumper {

    public static void dumpConstantPool(String basePackage, String outputJsonPath) {
        Map<String, Object> result = new HashMap<>();

        try (ScanResult scanResult = new ClassGraph()
            .enableClassInfo()
            .acceptPackages(basePackage)
            .scan()) {

            scanResult.getAllClasses().forEach(classInfo -> {
                String className = classInfo.getName();
                if (!className.endsWith("$ConstantPool")) return;

                try {
                    Class<?> clazz = Class.forName(className);
                    for (Field field : clazz.getDeclaredFields()) {
                        if (!java.lang.reflect.Modifier.isStatic(field.getModifiers())) continue;

                        field.setAccessible(true);
                        Object value = field.get(null);
                        if (value instanceof String) {
                            continue;
                        }

                        String key = className.substring(className.lastIndexOf('.') + 1) + "." + field.getName();
                        result.put(key, value);
                    }
                } catch (Throwable t) {
                    System.err.println("Failed to reflect: " + className + " - " + t);
                }
            });

            // 输出为 JSON
            Gson gson = new GsonBuilder()
                .serializeSpecialFloatingPointValues() // 允许 Infinity / NaN
                .setPrettyPrinting()
                .create();

            try (FileWriter writer = new FileWriter(outputJsonPath)) {
                gson.toJson(result, writer);
                System.out.println("ConstantPool dumped to " + outputJsonPath);
            } catch (Exception e) {
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
