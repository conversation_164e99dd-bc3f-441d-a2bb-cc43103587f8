name: Publish Development Build
on: push

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Wrapper Validation
        uses: gradle/actions/wrapper-validation@v3
      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: adopt
      - name: Build with Gradle
        run: ./gradlew build
      - name: Release
        uses: marvinpinto/action-automatic-releases@latest
        with:
          repo_token: '${{ secrets.GITHUB_TOKEN }}'
          automatic_release_tag: latest
          prerelease: true
          title: Dev Build
          files: |
            ./build/libs/*.jar
