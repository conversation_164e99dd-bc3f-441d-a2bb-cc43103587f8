name: Build Pull Request Artifacts
on: pull_request

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
      - name: Wrapper Validation
        uses: gradle/actions/wrapper-validation@v3
      - name: Set up Java
        uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: adopt
      - name: Build with Gradle
        run: ./gradlew build
      - name: Release
        uses: actions/upload-artifact@v4
        with:
          name: Artifacts
          path: build/libs/
